import z from "zod";
import { sql } from "drizzle-orm";

import { protectedProcedure, t } from "..";
import { schema } from "@/shared/database";
import { LLM_PROVIDERS } from "@/shared/providers";

export const keysRouter = t.router({
  list: protectedProcedure
    .input(z.object({ provider: z.literal(LLM_PROVIDERS).optional() }))
    .query(({ ctx, input }) => {
      return ctx.db.query.keys.findMany({
        where: (table, { eq }) => (input.provider ? eq(table.provider, input.provider) : undefined),
        orderBy: (table, { desc }) => [desc(table.provider), desc(table.status)],
      });
    }),

  disable: protectedProcedure
    .input(
      z.object({
        hash: z.string(),
        reason: z.string().min(1, "Reason is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const now = Math.floor(Date.now() / 1000);

      await ctx.db
        .update(schema.keys)
        .set({
          status: "disabled",
          disabledAt: now,
          disabledBy: "admin",
          metadata: sql`json_set(metadata, '$.disableReason', ${input.reason})`,
        })
        .where(sql`hash = ${input.hash}`);

      return { success: true };
    }),

  enable: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .update(schema.keys)
        .set({
          status: "unknown", // Will be checked by key checker
          disabledAt: null,
          disabledBy: null,
          metadata: sql`json_remove(metadata, '$.disableReason', '$.pendingDeletion')`,
        })
        .where(sql`hash = ${input.hash}`);

      return { success: true };
    }),

  markForDeletion: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const deletionTime = Math.floor(Date.now() / 1000) + 24 * 60 * 60; // 24 hours from now

      await ctx.db
        .update(schema.keys)
        .set({
          metadata: sql`json_set(metadata, '$.pendingDeletion', ${deletionTime})`,
        })
        .where(sql`hash = ${input.hash} AND (status = 'disabled' OR status = 'revoked')`);

      return { success: true };
    }),

  cancelDeletion: protectedProcedure
    .input(z.object({ hash: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db
        .update(schema.keys)
        .set({
          metadata: sql`json_remove(metadata, '$.pendingDeletion')`,
        })
        .where(sql`hash = ${input.hash}`);

      return { success: true };
    }),
});
